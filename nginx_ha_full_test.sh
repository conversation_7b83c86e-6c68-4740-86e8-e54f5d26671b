#!/bin/bash

# Comprehensive Nginx HA Testing Script
# Author: <PERSON><PERSON><PERSON><PERSON>
# Session: 32e8ab97-8e2f-40ed-9942-1b838074d5c8

# Configuration
NGINX1="************"
NGINX2="************"
PASSWORD="asdfasdf"
LOG_FILE="nginx_ha_test_$(date +%Y%m%d_%H%M%S).log"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

success() {
    log "${GREEN}[SUCCESS]${NC} $1"
}

error() {
    log "${RED}[ERROR]${NC} $1"
}

info() {
    log "${BLUE}[INFO]${NC} $1"
}

warning() {
    log "${YELLOW}[WARNING]${NC} $1"
}

# SSH execution function
ssh_exec() {
    local host=$1
    local cmd=$2
    local desc=$3
    
    info "Executing on $host: $desc"
    
    if sshpass -p "$PASSWORD" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no root@$host "$cmd" 2>/dev/null; then
        success "$desc completed on $host"
        return 0
    else
        error "$desc failed on $host"
        return 1
    fi
}

# Test 1: Basic connectivity
test_connectivity() {
    log "${BLUE}=== CONNECTIVITY TEST ===${NC}"
    
    # Ping test
    info "Testing ping connectivity..."
    if ping -c 2 $NGINX1 >/dev/null 2>&1; then
        success "nginx1 ($NGINX1) ping successful"
    else
        error "nginx1 ($NGINX1) ping failed"
    fi
    
    if ping -c 2 $NGINX2 >/dev/null 2>&1; then
        success "nginx2 ($NGINX2) ping successful"
    else
        error "nginx2 ($NGINX2) ping failed"
    fi
    
    # SSH test
    info "Testing SSH connectivity..."
    ssh_exec $NGINX1 "hostname && echo 'SSH to nginx1 successful'" "SSH connectivity"
    ssh_exec $NGINX2 "hostname && echo 'SSH to nginx2 successful'" "SSH connectivity"
}

# Test 2: System information
test_system_info() {
    log "${BLUE}=== SYSTEM INFORMATION ===${NC}"
    
    log "--- nginx1 System Info ---"
    ssh_exec $NGINX1 "hostname && cat /etc/os-release | grep PRETTY_NAME && uptime && free -h | head -2 && df -h / | tail -1" "System information"
    
    log "--- nginx2 System Info ---"
    ssh_exec $NGINX2 "hostname && cat /etc/os-release | grep PRETTY_NAME && uptime && free -h | head -2 && df -h / | tail -1" "System information"
}

# Test 3: Service status
test_services() {
    log "${BLUE}=== SERVICE STATUS ===${NC}"
    
    log "--- nginx1 Services ---"
    ssh_exec $NGINX1 "systemctl is-active nginx && systemctl is-active keepalived" "Service status"
    
    log "--- nginx2 Services ---"
    ssh_exec $NGINX2 "systemctl is-active nginx && systemctl is-active keepalived" "Service status"
}

# Test 4: HA status
test_ha_status() {
    log "${BLUE}=== HA STATUS ===${NC}"
    
    info "Checking VIP assignment..."
    log "--- nginx1 VIP Status ---"
    ssh_exec $NGINX1 "ip addr show | grep -E '172\.16\.2\.' | grep -v '172\.16\.2\.102'" "VIP check"
    
    log "--- nginx2 VIP Status ---"
    ssh_exec $NGINX2 "ip addr show | grep -E '172\.16\.2\.' | grep -v '172\.16\.2\.103'" "VIP check"
}

# Test 5: HTTP service
test_http() {
    log "${BLUE}=== HTTP SERVICE TEST ===${NC}"
    
    info "Testing HTTP access..."
    
    # Test nginx1
    if curl -s --connect-timeout 5 http://$NGINX1 >/dev/null; then
        response_time=$(curl -o /dev/null -s -w "%{time_total}" http://$NGINX1)
        success "nginx1 HTTP accessible (${response_time}s)"
    else
        error "nginx1 HTTP not accessible"
    fi
    
    # Test nginx2
    if curl -s --connect-timeout 5 http://$NGINX2 >/dev/null; then
        response_time=$(curl -o /dev/null -s -w "%{time_total}" http://$NGINX2)
        success "nginx2 HTTP accessible (${response_time}s)"
    else
        error "nginx2 HTTP not accessible"
    fi
}

# Test 6: Configuration check
test_config() {
    log "${BLUE}=== CONFIGURATION CHECK ===${NC}"
    
    log "--- nginx1 Configuration ---"
    ssh_exec $NGINX1 "nginx -v 2>&1 && nginx -t 2>&1" "Nginx configuration"
    
    log "--- nginx2 Configuration ---"
    ssh_exec $NGINX2 "nginx -v 2>&1 && nginx -t 2>&1" "Nginx configuration"
    
    log "--- Keepalived Configuration ---"
    ssh_exec $NGINX1 "cat /etc/keepalived/keepalived.conf | grep -E '(priority|state|interface)' 2>/dev/null || echo 'Keepalived config not found'" "Keepalived config"
    ssh_exec $NGINX2 "cat /etc/keepalived/keepalived.conf | grep -E '(priority|state|interface)' 2>/dev/null || echo 'Keepalived config not found'" "Keepalived config"
}

# Test 7: Failover test
test_failover() {
    log "${BLUE}=== FAILOVER TEST ===${NC}"
    
    warning "This test will temporarily stop nginx on one node"
    read -p "Continue with failover test? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info "Skipping failover test"
        return
    fi
    
    # Identify master
    info "Identifying current master node..."
    nginx1_vip=$(sshpass -p "$PASSWORD" ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no root@$NGINX1 "ip addr | grep -E '172\.16\.2\.(254|100|200)'" 2>/dev/null)
    nginx2_vip=$(sshpass -p "$PASSWORD" ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no root@$NGINX2 "ip addr | grep -E '172\.16\.2\.(254|100|200)'" 2>/dev/null)
    
    if [[ -n "$nginx1_vip" ]]; then
        master="nginx1"
        master_ip=$NGINX1
        backup="nginx2"
        backup_ip=$NGINX2
        success "nginx1 is current master"
    elif [[ -n "$nginx2_vip" ]]; then
        master="nginx2"
        master_ip=$NGINX2
        backup="nginx1"
        backup_ip=$NGINX1
        success "nginx2 is current master"
    else
        error "No VIP found on either node"
        return
    fi
    
    # Record start time
    start_time=$(date +%s.%N)
    
    # Stop nginx on master
    info "Stopping nginx on $master to trigger failover..."
    ssh_exec $master_ip "systemctl stop nginx" "Stop nginx"
    
    # Monitor failover
    info "Monitoring failover..."
    for i in {1..30}; do
        sleep 1
        backup_vip=$(sshpass -p "$PASSWORD" ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no root@$backup_ip "ip addr | grep -E '172\.16\.2\.(254|100|200)'" 2>/dev/null)
        
        if [[ -n "$backup_vip" ]]; then
            end_time=$(date +%s.%N)
            failover_time=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "calculated")
            success "Failover completed! VIP migrated to $backup in ${failover_time} seconds"
            break
        fi
        echo -n "."
    done
    echo ""
    
    # Restart nginx for recovery
    info "Restarting nginx on $master for recovery..."
    ssh_exec $master_ip "systemctl start nginx" "Start nginx"
    
    sleep 3
    info "Final VIP status after recovery:"
    ssh_exec $NGINX1 "ip addr | grep -E '172\.16\.2\.(254|100|200)' || echo 'No VIP on nginx1'" "VIP check"
    ssh_exec $NGINX2 "ip addr | grep -E '172\.16\.2\.(254|100|200)' || echo 'No VIP on nginx2'" "VIP check"
}

# Main execution
main() {
    log "${BLUE}=== NGINX HA COMPREHENSIVE TEST ===${NC}"
    log "Session: 32e8ab97-8e2f-40ed-9942-1b838074d5c8"
    log "Started: $(date)"
    log "nginx1: $NGINX1"
    log "nginx2: $NGINX2"
    log "Log file: $LOG_FILE"
    echo ""
    
    # Check prerequisites
    if ! command -v sshpass >/dev/null 2>&1; then
        error "sshpass not found. Please install it first."
        info "macOS: brew install hudochenkov/sshpass/sshpass"
        info "Ubuntu: sudo apt-get install sshpass"
        exit 1
    fi
    
    if ! command -v bc >/dev/null 2>&1; then
        warning "bc not found. Installing for time calculations..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install bc 2>/dev/null || true
        else
            sudo apt-get install -y bc 2>/dev/null || true
        fi
    fi
    
    # Run tests
    test_connectivity
    echo ""
    
    test_system_info
    echo ""
    
    test_services
    echo ""
    
    test_ha_status
    echo ""
    
    test_http
    echo ""
    
    test_config
    echo ""
    
    test_failover
    echo ""
    
    log "${GREEN}=== TESTING COMPLETED ===${NC}"
    log "Completed: $(date)"
    log "Results saved to: $LOG_FILE"
    
    success "All tests completed! Check $LOG_FILE for detailed results."
}

# Run main function
main "$@"
