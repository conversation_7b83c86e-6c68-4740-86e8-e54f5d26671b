#!/bin/bash

# Manual SSH Test Script for Nginx HA
# Run this script manually in your terminal

echo "=== SSH Test for Nginx HA Nodes ==="
echo "nginx1: ************"
echo "nginx2: ************"
echo "Password: asdfasdf"
echo ""

echo "Step 1: Testing ping connectivity..."
echo "Command: ping -c 3 ************"
ping -c 3 ************

echo ""
echo "Command: ping -c 3 ************"
ping -c 3 ************

echo ""
echo "Step 2: Testing SSH connectivity..."
echo "You may need to enter password 'asdfasdf' for each connection"
echo ""

echo "Testing nginx1..."
echo "Command: ssh root@************ 'hostname && uptime'"
ssh root@************ 'hostname && uptime'

echo ""
echo "Testing nginx2..."
echo "Command: ssh root@************ 'hostname && uptime'"
ssh root@************ 'hostname && uptime'

echo ""
echo "SSH test completed!"
