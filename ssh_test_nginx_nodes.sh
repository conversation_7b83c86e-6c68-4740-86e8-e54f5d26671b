#!/bin/bash

# SSH Testing Script for Nginx HA Nodes
# Author: <PERSON><PERSON><PERSON><PERSON>
# Date: $(date)

# Configuration
NGINX1_IP="***********"
NGINX2_IP="***********"
VIP="************"
USERNAME="root"
PASSWORD="asdfasdf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to execute SSH command with password
ssh_exec() {
    local host=$1
    local command=$2
    local description=$3
    
    print_status "Executing on $host: $description"
    
    # Using sshpass for password authentication
    if command -v sshpass >/dev/null 2>&1; then
        result=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$USERNAME@$host" "$command" 2>/dev/null)
        if [ $? -eq 0 ]; then
            print_success "$description completed on $host"
            echo "$result"
            return 0
        else
            print_error "$description failed on $host"
            return 1
        fi
    else
        print_error "sshpass not installed. Installing..."
        # Try to install sshpass
        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y sshpass
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y sshpass
        else
            print_error "Cannot install sshpass. Please install manually."
            return 1
        fi
        
        # Retry after installation
        result=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$USERNAME@$host" "$command" 2>/dev/null)
        if [ $? -eq 0 ]; then
            print_success "$description completed on $host"
            echo "$result"
            return 0
        else
            print_error "$description failed on $host"
            return 1
        fi
    fi
}

# Function to test SSH connectivity
test_ssh_connectivity() {
    print_status "=== TESTING SSH CONNECTIVITY ==="
    
    # Test nginx1
    print_status "Testing SSH connection to nginx1 ($NGINX1_IP)"
    if ssh_exec "$NGINX1_IP" "hostname && uptime" "Basic connectivity test"; then
        print_success "nginx1 SSH connection successful"
    else
        print_error "nginx1 SSH connection failed"
        return 1
    fi
    
    echo ""
    
    # Test nginx2
    print_status "Testing SSH connection to nginx2 ($NGINX2_IP)"
    if ssh_exec "$NGINX2_IP" "hostname && uptime" "Basic connectivity test"; then
        print_success "nginx2 SSH connection successful"
    else
        print_error "nginx2 SSH connection failed"
        return 1
    fi
    
    echo ""
    return 0
}

# Function to check current HA status
check_ha_status() {
    print_status "=== CHECKING HA STATUS ==="
    
    # Check VIP on both nodes
    print_status "Checking VIP ($VIP) assignment"
    
    echo "--- nginx1 VIP status ---"
    ssh_exec "$NGINX1_IP" "ip a | grep $VIP || echo 'VIP not found on nginx1'" "VIP check"
    
    echo ""
    echo "--- nginx2 VIP status ---"
    ssh_exec "$NGINX2_IP" "ip a | grep $VIP || echo 'VIP not found on nginx2'" "VIP check"
    
    echo ""
    
    # Check nginx status
    print_status "Checking nginx service status"
    
    echo "--- nginx1 service status ---"
    ssh_exec "$NGINX1_IP" "systemctl is-active nginx && systemctl is-enabled nginx" "Nginx status check"
    
    echo ""
    echo "--- nginx2 service status ---"
    ssh_exec "$NGINX2_IP" "systemctl is-active nginx && systemctl is-enabled nginx" "Nginx status check"
    
    echo ""
    
    # Check keepalived status
    print_status "Checking keepalived service status"
    
    echo "--- nginx1 keepalived status ---"
    ssh_exec "$NGINX1_IP" "systemctl is-active keepalived && systemctl is-enabled keepalived" "Keepalived status check"
    
    echo ""
    echo "--- nginx2 keepalived status ---"
    ssh_exec "$NGINX2_IP" "systemctl is-active keepalived && systemctl is-enabled keepalived" "Keepalived status check"
    
    echo ""
}

# Function to test basic failover
test_basic_failover() {
    print_status "=== TESTING BASIC FAILOVER ==="
    
    # First, identify which node has VIP
    print_status "Identifying current master node"
    
    nginx1_has_vip=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$NGINX1_IP" "ip a | grep $VIP" 2>/dev/null)
    nginx2_has_vip=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$NGINX2_IP" "ip a | grep $VIP" 2>/dev/null)
    
    if [ -n "$nginx1_has_vip" ]; then
        master_node="nginx1"
        master_ip="$NGINX1_IP"
        backup_node="nginx2"
        backup_ip="$NGINX2_IP"
        print_success "nginx1 is currently the master (has VIP)"
    elif [ -n "$nginx2_has_vip" ]; then
        master_node="nginx2"
        master_ip="$NGINX2_IP"
        backup_node="nginx1"
        backup_ip="$NGINX1_IP"
        print_success "nginx2 is currently the master (has VIP)"
    else
        print_error "No node has VIP! HA might be misconfigured"
        return 1
    fi
    
    echo ""
    
    # Test VIP accessibility before failover
    print_status "Testing VIP accessibility before failover"
    if curl -s --connect-timeout 5 "http://$VIP" > /dev/null; then
        print_success "VIP is accessible before failover"
    else
        print_error "VIP is not accessible before failover"
    fi
    
    echo ""
    
    # Record start time
    start_time=$(date +%s.%N)
    
    # Stop nginx on master node to trigger failover
    print_status "Stopping nginx on master node ($master_node) to trigger failover"
    ssh_exec "$master_ip" "systemctl stop nginx" "Stop nginx service"
    
    # Monitor VIP migration
    print_status "Monitoring VIP migration..."
    
    failover_detected=false
    timeout=30
    elapsed=0
    
    while [ $elapsed -lt $timeout ]; do
        sleep 1
        elapsed=$((elapsed + 1))
        
        # Check if backup node now has VIP
        backup_has_vip=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$backup_ip" "ip a | grep $VIP" 2>/dev/null)
        
        if [ -n "$backup_has_vip" ]; then
            end_time=$(date +%s.%N)
            failover_time=$(echo "$end_time - $start_time" | bc -l)
            print_success "Failover completed! VIP migrated to $backup_node in ${failover_time} seconds"
            failover_detected=true
            break
        fi
        
        echo -n "."
    done
    
    echo ""
    
    if [ "$failover_detected" = false ]; then
        print_error "Failover did not complete within $timeout seconds"
        return 1
    fi
    
    # Test VIP accessibility after failover
    print_status "Testing VIP accessibility after failover"
    sleep 2  # Give some time for services to stabilize
    
    if curl -s --connect-timeout 5 "http://$VIP" > /dev/null; then
        print_success "VIP is accessible after failover"
    else
        print_error "VIP is not accessible after failover"
    fi
    
    echo ""
    
    # Restart nginx on original master for recovery
    print_status "Restarting nginx on original master ($master_node) for recovery"
    ssh_exec "$master_ip" "systemctl start nginx" "Start nginx service"
    
    # Wait a bit and check if VIP returns (depending on priority configuration)
    sleep 5
    print_status "Checking VIP status after recovery"
    
    echo "--- Current VIP assignment ---"
    ssh_exec "$NGINX1_IP" "ip a | grep $VIP || echo 'VIP not on nginx1'" "VIP check nginx1"
    ssh_exec "$NGINX2_IP" "ip a | grep $VIP || echo 'VIP not on nginx2'" "VIP check nginx2"
    
    echo ""
}

# Function to test system information
check_system_info() {
    print_status "=== SYSTEM INFORMATION ==="
    
    echo "--- nginx1 system info ---"
    ssh_exec "$NGINX1_IP" "echo 'Hostname:' && hostname && echo 'OS:' && cat /etc/os-release | grep PRETTY_NAME && echo 'Memory:' && free -h && echo 'Disk:' && df -h /" "System information"
    
    echo ""
    echo "--- nginx2 system info ---"
    ssh_exec "$NGINX2_IP" "echo 'Hostname:' && hostname && echo 'OS:' && cat /etc/os-release | grep PRETTY_NAME && echo 'Memory:' && free -h && echo 'Disk:' && df -h /" "System information"
    
    echo ""
}

# Function to check nginx configuration
check_nginx_config() {
    print_status "=== NGINX CONFIGURATION CHECK ==="
    
    echo "--- nginx1 configuration ---"
    ssh_exec "$NGINX1_IP" "nginx -t && echo 'Config test passed'" "Nginx config test"
    ssh_exec "$NGINX1_IP" "nginx -V 2>&1 | head -1" "Nginx version"
    
    echo ""
    echo "--- nginx2 configuration ---"
    ssh_exec "$NGINX2_IP" "nginx -t && echo 'Config test passed'" "Nginx config test"
    ssh_exec "$NGINX2_IP" "nginx -V 2>&1 | head -1" "Nginx version"
    
    echo ""
}

# Function to check keepalived configuration
check_keepalived_config() {
    print_status "=== KEEPALIVED CONFIGURATION CHECK ==="
    
    echo "--- nginx1 keepalived config ---"
    ssh_exec "$NGINX1_IP" "cat /etc/keepalived/keepalived.conf | grep -E '(priority|state|interface)'" "Keepalived config check"
    
    echo ""
    echo "--- nginx2 keepalived config ---"
    ssh_exec "$NGINX2_IP" "cat /etc/keepalived/keepalived.conf | grep -E '(priority|state|interface)'" "Keepalived config check"
    
    echo ""
}

# Main execution
main() {
    print_status "Starting SSH Testing for Nginx HA Nodes"
    print_status "nginx1: $NGINX1_IP"
    print_status "nginx2: $NGINX2_IP"
    print_status "VIP: $VIP"
    echo ""
    
    # Test SSH connectivity first
    if ! test_ssh_connectivity; then
        print_error "SSH connectivity test failed. Exiting."
        exit 1
    fi
    
    # Check system information
    check_system_info
    
    # Check current HA status
    check_ha_status
    
    # Check nginx configuration
    check_nginx_config
    
    # Check keepalived configuration
    check_keepalived_config
    
    # Ask user if they want to test failover
    echo ""
    read -p "Do you want to test failover? This will temporarily stop nginx on the master node (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        test_basic_failover
    else
        print_status "Skipping failover test"
    fi
    
    print_success "SSH testing completed!"
}

# Check if bc is installed (needed for time calculation)
if ! command -v bc >/dev/null 2>&1; then
    print_warning "bc not found, installing..."
    if command -v apt-get >/dev/null 2>&1; then
        sudo apt-get update && sudo apt-get install -y bc
    elif command -v yum >/dev/null 2>&1; then
        sudo yum install -y bc
    fi
fi

# Run main function
main "$@"
