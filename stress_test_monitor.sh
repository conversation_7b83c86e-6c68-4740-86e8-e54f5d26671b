#!/bin/bash

# Stress Test Monitoring Script
# Monitors system resources during load testing

LOG_FILE="stress_test_$(date +%Y%m%d_%H%M%S).log"
PUBLIC_IP="**************"

echo "=== NGINX HA STRESS TEST MONITORING ===" | tee -a $LOG_FILE
echo "Start Time: $(date)" | tee -a $LOG_FILE
echo "Target: $PUBLIC_IP" | tee -a $LOG_FILE
echo "Log File: $LOG_FILE" | tee -a $LOG_FILE
echo "=========================================" | tee -a $LOG_FILE

# Function to monitor server resources
monitor_server_resources() {
    local server=$1
    local server_ip=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S.%3N')
    
    # Get CPU, Memory, Load, and Connection info
    local stats=$(timeout 10 sshpass -p 'asdfasdf' ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no nginx1@$server_ip \
        "top -bn1 | grep 'Cpu(s)' | awk '{print \$2}' | cut -d'%' -f1; \
         free | grep Mem | awk '{printf \"%.1f\", \$3/\$2 * 100.0}'; \
         uptime | awk -F'load average:' '{print \$2}' | awk '{print \$1}' | tr -d ','; \
         ss -tuln | grep :80 | wc -l; \
         ps aux | grep nginx | grep -v grep | wc -l; \
         systemctl is-active nginx" 2>/dev/null || echo "unreachable unreachable unreachable unreachable unreachable unreachable")
    
    local cpu=$(echo $stats | awk '{print $1}')
    local mem=$(echo $stats | awk '{print $2}')
    local load=$(echo $stats | awk '{print $3}')
    local conn=$(echo $stats | awk '{print $4}')
    local nginx_proc=$(echo $stats | awk '{print $5}')
    local nginx_status=$(echo $stats | awk '{print $6}')
    
    echo "[$timestamp] $server - CPU:${cpu}% MEM:${mem}% LOAD:${load} CONN:${conn} PROC:${nginx_proc} STATUS:${nginx_status}" | tee -a $LOG_FILE
}

# Function to test response time
test_response() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S.%3N')
    local start_time=$(date +%s.%3N)
    
    local result=$(curl -w "time_total:%{time_total}|time_connect:%{time_connect}|http_code:%{http_code}" \
                   -o /dev/null -s --connect-timeout 3 --max-time 8 "http://$PUBLIC_IP" 2>/dev/null)
    
    local end_time=$(date +%s.%3N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    
    if [ $? -eq 0 ]; then
        echo "[$timestamp] RESPONSE - $result | duration:${duration}s" | tee -a $LOG_FILE
    else
        echo "[$timestamp] RESPONSE - FAILED | duration:${duration}s" | tee -a $LOG_FILE
    fi
}

# Function to check VIP location
check_vip_location() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S.%3N')
    
    local vip_on_nginx1=$(timeout 5 sshpass -p 'asdfasdf' ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no nginx1@************ \
                         "ip addr show ens18 | grep '************' >/dev/null && echo 'YES' || echo 'NO'" 2>/dev/null || echo "unknown")
    
    local vip_on_nginx2=$(timeout 5 sshpass -p 'asdfasdf' ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no nginx2@************ \
                         "ip addr show ens18 | grep '************' >/dev/null && echo 'YES' || echo 'NO'" 2>/dev/null || echo "unknown")
    
    local vip_location="unknown"
    if [ "$vip_on_nginx1" = "YES" ]; then
        vip_location="nginx1"
    elif [ "$vip_on_nginx2" = "YES" ]; then
        vip_location="nginx2"
    fi
    
    echo "[$timestamp] VIP_LOCATION - $vip_location (nginx1:$vip_on_nginx1 nginx2:$vip_on_nginx2)" | tee -a $LOG_FILE
}

echo "Starting stress test monitoring..."
echo "Monitoring every 2 seconds..."

# Initial status
monitor_server_resources "nginx1" "************"
monitor_server_resources "nginx2" "************"
check_vip_location

# Continuous monitoring loop
while true; do
    test_response
    
    # Monitor resources every 5 iterations (10 seconds)
    if [ $(($(date +%s) % 10)) -eq 0 ]; then
        monitor_server_resources "nginx1" "************"
        monitor_server_resources "nginx2" "************"
        check_vip_location
    fi
    
    sleep 2
done
