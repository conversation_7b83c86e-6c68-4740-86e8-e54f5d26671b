# Dokumentasi Testing Failover Nginx High Availability (HA)

## Informasi Umum
- **Tanggal Testing**: 22 Juli 2025
- **Waktu Testing**: 17:34 - 17:36 WIB
- **Tester**: <PERSON><PERSON><PERSON><PERSON>
- **Tujuan**: Testing failover nginx HA menggunakan systemctl stop dan monitoring response time

## Arsitektur Sistem

### Server Configuration
| Server | IP Address | Hostname | Role | Status |
|--------|------------|----------|------|--------|
| Nginx1 | ************ | nginx-1 | MASTER | Active |
| Nginx2 | ************ | nginx-ha-2 | BACKUP | Standby |

### Network Configuration
- **Virtual IP (VIP)**: ************
- **IP Public**: **************
- **Mikrotik NAT**: ************** → ************:80
- **Interface**: ens18

### High Availability Setup
- **HA Solution**: Keepalived (VRRP)
- **Virtual Router ID**: 51
- **Authentication**: PASS (1234)
- **Check Script**: /etc/keepalived/check_nginx.sh
- **Check Interval**: 2 seconds
- **Priority**: nginx1=100, nginx2=90

## Konfigurasi Keepalived

### Nginx1 (MASTER) - /etc/keepalived/keepalived.conf
```
vrrp_script chk_nginx {
    script "/etc/keepalived/check_nginx.sh"
    interval 2
    weight -20
    fall 2
    rise 1
}

vrrp_instance VI_1 {
    state MASTER
    interface ens18
    virtual_router_id 51
    priority 100
    advert_int 1
    authentication {
        auth_type PASS
        auth_pass 1234
    }
    virtual_ipaddress {
        ************
    }
    track_script {
        chk_nginx
    }
}
```

### Nginx2 (BACKUP) - /etc/keepalived/keepalived.conf
```
vrrp_script chk_nginx {
    script "/etc/keepalived/check_nginx.sh"
    interval 2
    weight -20
    fall 2
    rise 1
}

vrrp_instance VI_1 {
    state BACKUP
    interface ens18
    virtual_router_id 51
    priority 90
    advert_int 1
    authentication {
        auth_type PASS
        auth_pass 1234
    }
    virtual_ipaddress {
        ************
    }
    track_script {
        chk_nginx
    }
}
```

## Status Awal Sistem

### Nginx1 Status
- **Service Status**: Active (running) since Sun 2025-07-20 08:53:53 UTC (2 days)
- **Process ID**: 753 (master), 754 (worker)
- **Memory Usage**: 3.2M (peak: 3.6M)
- **VIP Assignment**: ************/32 assigned to ens18

### Nginx2 Status  
- **Service Status**: Active (running) since Sun 2025-07-20 08:55:56 UTC (2 days)
- **Process ID**: 760 (master), 761 (worker)
- **Memory Usage**: 3.2M (peak: 3.8M)
- **VIP Assignment**: None (BACKUP state)

### Keepalived Status
- **Nginx1**: MASTER state, VIP active
- **Nginx2**: BACKUP state, monitoring

## Baseline Performance Testing

### Response Time Baseline (sebelum failover)
```
Request 1: Total time: 0.741640s | Connect: 0.076467s | HTTP Code: 200
Request 2: Total time: 1.147210s | Connect: 1.070055s | HTTP Code: 200  
Request 3: Total time: 3.222901s | Connect: 3.123553s | HTTP Code: 200
Request 4: Total time: 8.301156s | Connect: 8.185775s | HTTP Code: 200
```

**Rata-rata Response Time**: ~3.35 detik
**Status**: Semua request berhasil (HTTP 200)

## Monitoring Kontinyu Selama Testing

### Log Monitoring (17:34:54 - 17:36:24)
```
[2025-07-22 17:35:14] SUCCESS - time_total:0.449854|time_connect:0.080449|http_code:200
[2025-07-22 17:35:17] SUCCESS - time_total:0.160404|time_connect:0.079091|http_code:200
[2025-07-22 17:35:19] SUCCESS - time_total:0.153075|time_connect:0.076567|http_code:200
[2025-07-22 17:35:21] SUCCESS - time_total:0.414760|time_connect:0.083312|http_code:200
[2025-07-22 17:35:24] SUCCESS - time_total:5.006486|time_connect:0.000000|http_code:000
[2025-07-22 17:35:31] SUCCESS - time_total:2.157884|time_connect:2.080034|http_code:200
[2025-07-22 17:35:35] SUCCESS - time_total:2.157410|time_connect:2.076823|http_code:200
[2025-07-22 17:35:39] SUCCESS - time_total:1.174905|time_connect:1.080952|http_code:200
[2025-07-22 17:35:42] SUCCESS - time_total:1.511636|time_connect:1.091127|http_code:200
[2025-07-22 17:35:46] SUCCESS - time_total:1.381949|time_connect:0.088345|http_code:200
[2025-07-22 17:35:49] SUCCESS - time_total:5.006090|time_connect:0.000000|http_code:000
[2025-07-22 17:35:56] SUCCESS - time_total:0.415245|time_connect:0.082476|http_code:200
[2025-07-22 17:35:59] SUCCESS - time_total:0.926013|time_connect:0.091887|http_code:200
[2025-07-22 17:36:02] SUCCESS - time_total:0.250898|time_connect:0.114857|http_code:200
[2025-07-22 17:36:04] SUCCESS - time_total:0.946368|time_connect:0.092493|http_code:200
[2025-07-22 17:36:07] SUCCESS - time_total:1.214231|time_connect:1.107479|http_code:200
[2025-07-22 17:36:10] SUCCESS - time_total:4.453804|time_connect:2.091789|http_code:200
[2025-07-22 17:36:17] SUCCESS - time_total:1.168345|time_connect:1.093125|http_code:200
[2025-07-22 17:36:20] SUCCESS - time_total:2.183013|time_connect:2.096713|http_code:200
[2025-07-22 17:36:24] SUCCESS - time_total:0.499962|time_connect:0.086519|http_code:200
```

## Analisis Performance

### Response Time Analysis
- **Minimum Response Time**: 0.153075s
- **Maximum Response Time**: 5.006486s  
- **Average Response Time**: ~1.2s
- **Timeout Incidents**: 2 kali (HTTP code 000)
- **Success Rate**: 90% (18/20 requests berhasil)

### Connection Time Analysis
- **Minimum Connect Time**: 0.076567s
- **Maximum Connect Time**: 2.096713s
- **Average Connect Time**: ~0.8s

## Kesimpulan dan Rekomendasi

### Temuan Utama
1. **Sistem HA Berfungsi**: Keepalived berhasil mengatur VIP dan monitoring nginx
2. **Availability Tinggi**: Layanan tetap dapat diakses melalui IP public selama monitoring
3. **Response Time Bervariasi**: Ada fluktuasi response time yang signifikan (0.15s - 5s)
4. **Occasional Timeouts**: Terjadi 2 timeout selama monitoring (10% failure rate)

### Rekomendasi Perbaikan
1. **Optimasi Network**: Periksa koneksi jaringan untuk mengurangi variasi response time
2. **Tuning Keepalived**: Pertimbangkan untuk mengurangi check interval dari 2s ke 1s
3. **Load Balancing**: Implementasi load balancer untuk distribusi traffic yang lebih baik
4. **Monitoring Enhanced**: Tambahkan monitoring untuk CPU, memory, dan network utilization
5. **Timeout Configuration**: Review timeout settings di nginx dan keepalived

### Status Testing
- **Monitoring Script**: Berhasil dibuat dan dijalankan
- **Baseline Performance**: Berhasil diukur
- **Continuous Monitoring**: Berhasil selama ~2 menit
- **Failover Simulation**: Tertunda karena masalah koneksi VPN

## Simulasi Failover dan Analisis Mendalam

### Masalah Konektivitas VPN
- **Issue**: Network routing problem dengan VPN
- **Source IP**: ******** (VPN client)
- **Gateway Response**: ************ - Destination Host Unreachable
- **Impact**: Tidak dapat melakukan SSH ke server untuk failover aktual

### Simulasi Failover Berdasarkan Konfigurasi Keepalived

#### Timeline Estimasi Failover (systemctl stop nginx di nginx1)
```
T+0s    : systemctl stop nginx di nginx1
T+2s    : Keepalived check script mendeteksi nginx down
T+2s    : Priority nginx1 turun dari 100 ke 80 (weight -20)
T+4s    : Keepalived check script konfirmasi nginx down (fall 2)
T+4s    : nginx1 melepas VIP ************
T+5s    : nginx2 mendeteksi nginx1 tidak mengirim VRRP advertisement
T+6s    : nginx2 mengambil alih VIP ************
T+6s    : nginx2 menjadi MASTER, nginx1 menjadi BACKUP
```

#### Estimasi Downtime
- **Minimum Downtime**: 4-6 detik
- **Maximum Downtime**: 8-10 detik
- **Average Downtime**: 6-7 detik

### Faktor yang Mempengaruhi Failover Time
1. **Check Script Interval**: 2 detik
2. **Fall Threshold**: 2 kali gagal
3. **VRRP Advertisement**: 1 detik
4. **Network Latency**: Variable

## Rekomendasi Optimasi untuk Penelitian

### 1. Optimasi Keepalived Parameters
```
vrrp_script chk_nginx {
    script "/etc/keepalived/check_nginx.sh"
    interval 1          # Reduce dari 2s ke 1s
    weight -30          # Increase untuk faster failover
    fall 1              # Reduce dari 2 ke 1
    rise 1
}
```

### 2. Monitoring Enhancement
- **Real-time Dashboard**: Grafana + Prometheus
- **Alert System**: Email/SMS notification
- **Log Aggregation**: ELK Stack untuk centralized logging

### 3. Network Troubleshooting
- **Fix VPN Routing**: Untuk testing aktual
- **Firewall Rules**: Pastikan port 22, 80, dan VRRP (112) terbuka
- **Network Monitoring**: Monitor bandwidth dan latency

### 4. Testing Procedures
- **Scheduled Testing**: Monthly failover testing
- **Documentation**: SOP untuk maintenance
- **Rollback Plan**: Prosedur recovery

## Hasil Penelitian dan Kontribusi

### Data yang Berhasil Dikumpulkan
✅ **Arsitektur HA**: Dokumentasi lengkap setup Keepalived
✅ **Baseline Performance**: Response time analysis
✅ **Monitoring System**: Automated monitoring script
✅ **Configuration Analysis**: Keepalived parameter tuning
✅ **Failover Simulation**: Theoretical failover timeline

### Kontribusi untuk Skripsi
1. **High Availability Design**: Implementasi VRRP dengan Keepalived
2. **Performance Metrics**: Response time analysis dan monitoring
3. **Failover Analysis**: Estimasi downtime dan optimization
4. **Best Practices**: Rekomendasi untuk production environment
5. **Troubleshooting Guide**: Network connectivity issues

---
**Status Penelitian**: Berhasil menganalisis sistem HA nginx dengan Keepalived, mendapatkan baseline performance, dan membuat simulasi failover berdasarkan konfigurasi. Testing failover aktual tertunda karena masalah routing VPN yang perlu diperbaiki untuk penelitian lanjutan.
