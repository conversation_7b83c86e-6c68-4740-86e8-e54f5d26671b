#!/bin/bash

# Simple SSH Test for Nginx HA
# Run this manually in your terminal

echo "=== SSH Test for Nginx HA ==="
echo "Date: $(date)"
echo ""

# Create directory if it doesn't exist
mkdir -p ~/nginx-ha-testing
cd ~/nginx-ha-testing

echo "Current directory: $(pwd)"
echo ""

echo "Testing SSH connections to nginx nodes:"
echo "nginx1: ************"
echo "nginx2: ************"
echo "Password: asdfasdf"
echo ""

# Test basic connectivity first
echo "Step 1: Testing ping connectivity..."
echo "Command: ping -c 2 ************"
ping -c 2 ************

echo ""
echo "Command: ping -c 2 ************"
ping -c 2 ************

echo ""
echo "Step 2: Testing SSH connectivity..."
echo "Note: You'll need to enter password 'asdfasdf' when prompted"
echo ""

echo "Testing SSH to nginx1 (************)..."
echo "Command: ssh root@************ 'hostname && uptime'"
ssh root@************ 'hostname && uptime'

echo ""
echo "Testing SSH to nginx2 (************)..."
echo "Command: ssh root@************ 'hostname && uptime'"
ssh root@************ 'hostname && uptime'

echo ""
echo "SSH test completed!"
echo "If successful, you can proceed with HA testing."
