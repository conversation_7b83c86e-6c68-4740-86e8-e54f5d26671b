# MANUAL SSH TESTING INSTRUCTIONS

## Problem Solution
The terminal failed because directory `/Users/<USER>/Desktop/nginx-ha-testing` doesn't exist.

## Quick Fix - Run These Commands Manually:

### 1. Create Directory and Navigate
```bash
mkdir -p ~/nginx-ha-testing
cd ~/nginx-ha-testing
pwd
```

### 2. Test Basic Connectivity
```bash
# Test ping to nginx1
ping -c 3 ************

# Test ping to nginx2  
ping -c 3 ************
```

### 3. Test SSH Connections
```bash
# SSH to nginx1 (password: asdfasdf)
ssh root@************ "hostname && uptime"

# SSH to nginx2 (password: asdfasdf)
ssh root@************ "hostname && uptime"
```

### 4. If SSH Works, Test Services
```bash
# Check nginx1 services
ssh root@************ "systemctl status nginx && systemctl status keepalived"

# Check nginx2 services
ssh root@************ "systemctl status nginx && systemctl status keepalived"
```

### 5. Check HA Status
```bash
# Check VIP on nginx1
ssh root@************ "ip addr show | grep 172.16.2"

# Check VIP on nginx2
ssh root@************ "ip addr show | grep 172.16.2"
```

### 6. Test HTTP Services
```bash
# Test nginx1 HTTP
curl -I http://************

# Test nginx2 HTTP
curl -I http://************

# Test with timing
curl -o /dev/null -s -w "Time: %{time_total}s, HTTP: %{http_code}\n" http://************
curl -o /dev/null -s -w "Time: %{time_total}s, HTTP: %{http_code}\n" http://************
```

## Alternative: Use the Script
```bash
# Make script executable and run
chmod +x ssh_test_simple.sh
./ssh_test_simple.sh
```

## Expected Results:
- ✅ Ping should work to both IPs
- ✅ SSH should connect (you'll enter password: asdfasdf)
- ✅ Should see hostname and uptime from both servers
- ✅ Services should be running (nginx and keepalived)
- ✅ One server should have VIP assigned

## If SSH Fails:
1. Check if servers are running
2. Verify IP addresses are correct
3. Check firewall settings
4. Verify SSH service is running on servers

## Next Steps After SSH Works:
1. Run comprehensive HA testing
2. Test failover scenarios
3. Performance testing
4. Load testing with failover

---
**Run these commands in your terminal to start testing!**
