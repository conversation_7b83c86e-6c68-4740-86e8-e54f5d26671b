#!/bin/bash

# SSH Test Script for Nginx HA Nodes
echo "=== Testing SSH to Nginx HA Nodes ==="
echo "Date: $(date)"
echo ""

# Configuration
NGINX1="************"
NGINX2="************"
PASSWORD="asdfasdf"

echo "nginx1: $NGINX1"
echo "nginx2: $NGINX2"
echo "Password: $PASSWORD"
echo ""

# Test 1: Ping connectivity
echo "--- Step 1: Testing Ping Connectivity ---"
echo "Pinging nginx1..."
if ping -c 2 $NGINX1 >/dev/null 2>&1; then
    echo "✅ nginx1 ($NGINX1) is reachable"
else
    echo "❌ nginx1 ($NGINX1) is NOT reachable"
fi

echo "Pinging nginx2..."
if ping -c 2 $NGINX2 >/dev/null 2>&1; then
    echo "✅ nginx2 ($NGINX2) is reachable"
else
    echo "❌ nginx2 ($NGINX2) is NOT reachable"
fi

echo ""

# Test 2: SSH connectivity
echo "--- Step 2: Testing SSH Connectivity ---"
echo "Note: You'll need to enter password '$PASSWORD' when prompted"
echo ""

echo "Testing SSH to nginx1..."
echo "Command: ssh root@$NGINX1 'hostname && echo \"SSH successful to nginx1\" && uptime'"
ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no root@$NGINX1 'hostname && echo "SSH successful to nginx1" && uptime'

echo ""
echo "Testing SSH to nginx2..."
echo "Command: ssh root@$NGINX2 'hostname && echo \"SSH successful to nginx2\" && uptime'"
ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no root@$NGINX2 'hostname && echo "SSH successful to nginx2" && uptime'

echo ""
echo "SSH connectivity test completed!"
echo ""

# Ask if user wants to continue with service checks
read -p "SSH test done. Check nginx services? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "--- Step 3: Checking Nginx Services ---"
    
    echo "Checking nginx1 services..."
    ssh root@$NGINX1 'echo "=== nginx1 Services ===" && systemctl is-active nginx && systemctl is-active keepalived'
    
    echo ""
    echo "Checking nginx2 services..."
    ssh root@$NGINX2 'echo "=== nginx2 Services ===" && systemctl is-active nginx && systemctl is-active keepalived'
    
    echo ""
    echo "--- Step 4: Checking HA Status ---"
    
    echo "Checking VIP on nginx1..."
    ssh root@$NGINX1 'echo "nginx1 IP addresses:" && ip addr show | grep "inet " | grep -v "127.0.0.1"'
    
    echo ""
    echo "Checking VIP on nginx2..."
    ssh root@$NGINX2 'echo "nginx2 IP addresses:" && ip addr show | grep "inet " | grep -v "127.0.0.1"'
fi

echo ""
echo "Testing completed!"
