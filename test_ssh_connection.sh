#!/bin/bash

# Simple SSH Test Script for Nginx HA Nodes
# nginx1: ************
# nginx2: ************
# Password: asdfasdf

echo "=== Testing SSH Connection to Nginx Nodes ==="
echo "Date: $(date)"
echo ""

# Install sshpass if not available
if ! command -v sshpass >/dev/null 2>&1; then
    echo "Installing sshpass..."
    sudo apt-get update && sudo apt-get install -y sshpass
fi

# Test nginx1
echo "--- Testing nginx1 (************) ---"
sshpass -p "asdfasdf" ssh -o StrictHostKeyChecking=no root@************ "echo 'Connected to nginx1'; hostname; uptime"

echo ""

# Test nginx2
echo "--- Testing nginx2 (************) ---"
sshpass -p "asdfasdf" ssh -o StrictHostKeyChecking=no root@************ "echo 'Connected to nginx2'; hostname; uptime"

echo ""
echo "SSH connection test completed!"
