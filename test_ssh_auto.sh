#!/bin/bash

# Automated SSH Test Script for Nginx HA
echo "=== Automated SSH Test for Nginx HA ==="
echo "Date: $(date)"
echo ""

# Check if sshpass is installed
if ! command -v sshpass &> /dev/null; then
    echo "sshpass not found. Installing..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install hudochenkov/sshpass/sshpass
        else
            echo "Please install Homebrew first, then run: brew install hudochenkov/sshpass/sshpass"
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y sshpass
        elif command -v yum &> /dev/null; then
            sudo yum install -y sshpass
        else
            echo "Please install sshpass manually"
            exit 1
        fi
    fi
fi

echo "Testing connectivity to nginx nodes..."
echo ""

# Test nginx1
echo "--- Testing nginx1 (************) ---"
if sshpass -p "asdfasdf" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no root@************ "echo 'SSH successful'; hostname; uptime" 2>/dev/null; then
    echo "✅ nginx1 SSH connection successful"
else
    echo "❌ nginx1 SSH connection failed"
    echo "Trying with ping..."
    ping -c 2 ************
fi

echo ""

# Test nginx2
echo "--- Testing nginx2 (************) ---"
if sshpass -p "asdfasdf" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no root@************ "echo 'SSH successful'; hostname; uptime" 2>/dev/null; then
    echo "✅ nginx2 SSH connection successful"
else
    echo "❌ nginx2 SSH connection failed"
    echo "Trying with ping..."
    ping -c 2 ************
fi

echo ""
echo "Test completed at: $(date)"
