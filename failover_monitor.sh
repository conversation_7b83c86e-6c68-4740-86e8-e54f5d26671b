#!/bin/bash

# Enhanced Failover Monitoring Script
# Monitors response time and availability during nginx failover testing

LOG_FILE="failover_test_$(date +%Y%m%d_%H%M%S).log"
PUBLIC_IP="**************"
VIRTUAL_IP="************"

echo "=== NGINX HA FAILOVER TEST - ENHANCED ===" | tee -a $LOG_FILE
echo "Start Time: $(date)" | tee -a $LOG_FILE
echo "Public IP: $PUBLIC_IP" | tee -a $LOG_FILE
echo "Virtual IP: $VIRTUAL_IP" | tee -a $LOG_FILE
echo "Log File: $LOG_FILE" | tee -a $LOG_FILE
echo "=========================================" | tee -a $LOG_FILE

# Function to test connectivity and response time
test_connectivity() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S.%3N')
    local start_time=$(date +%s.%3N)

    local result=$(curl -w "time_total:%{time_total}|time_connect:%{time_connect}|time_starttransfer:%{time_starttransfer}|http_code:%{http_code}" \
                   -o /dev/null -s --connect-timeout 3 --max-time 8 "http://$PUBLIC_IP" 2>/dev/null)

    local end_time=$(date +%s.%3N)
    local duration=$(echo "$end_time - $start_time" | bc -l)

    if [ $? -eq 0 ]; then
        echo "[$timestamp] SUCCESS - $result | duration:${duration}s" | tee -a $LOG_FILE
    else
        echo "[$timestamp] FAILED - Connection timeout/error | duration:${duration}s" | tee -a $LOG_FILE
    fi
}

# Function to check which server is active with password
check_active_server() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S.%3N')

    # Check nginx1 status
    nginx1_status=$(timeout 5 sshpass -p 'asdfasdf' ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no nginx1@************ \
                   "systemctl is-active nginx" 2>/dev/null || echo "unreachable")

    # Check nginx2 status
    nginx2_status=$(timeout 5 sshpass -p 'asdfasdf' ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no nginx2@************ \
                   "systemctl is-active nginx" 2>/dev/null || echo "unreachable")

    # Check VIP location
    vip_on_nginx1=$(timeout 5 sshpass -p 'asdfasdf' ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no nginx1@************ \
                   "ip addr show ens18 | grep '************' >/dev/null && echo 'YES' || echo 'NO'" 2>/dev/null || echo "unknown")

    vip_on_nginx2=$(timeout 5 sshpass -p 'asdfasdf' ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no nginx2@************ \
                   "ip addr show ens18 | grep '************' >/dev/null && echo 'YES' || echo 'NO'" 2>/dev/null || echo "unknown")

    local vip_location="unknown"
    if [ "$vip_on_nginx1" = "YES" ]; then
        vip_location="nginx1"
    elif [ "$vip_on_nginx2" = "YES" ]; then
        vip_location="nginx2"
    fi

    echo "[$timestamp] STATUS - nginx1:$nginx1_status nginx2:$nginx2_status VIP_on:$vip_location" | tee -a $LOG_FILE
}

# Install sshpass if not available
if ! command -v sshpass &> /dev/null; then
    echo "Installing sshpass..."
    if command -v brew &> /dev/null; then
        brew install sshpass
    else
        echo "Please install sshpass manually"
        exit 1
    fi
fi

echo "Starting enhanced monitoring (press Ctrl+C to stop)..."
echo "Monitoring every 1 second for precise failover detection..."

# Initial status check
check_active_server

# Continuous monitoring loop with faster interval
while true; do
    test_connectivity
    # Check server status every 5 iterations (5 seconds)
    if [ $(($(date +%s) % 5)) -eq 0 ]; then
        check_active_server
    fi
    sleep 1
done
