# Simulasi dan Analisis Failover Nginx HA

## Kondisi Network Saat Testing
- **VPN Status**: Aktif namun ada masalah routing
- **Source IP**: ******** (VPN client)
- **Gateway Response**: ************ - Destination Host Unreachable
- **Public IP Access**: Timeout (tidak dapat diakses)

## Simulasi Failover Berdasarkan Konfigurasi Keepalived

### Skenario: systemctl stop nginx di nginx1 (************)

#### Timeline Failover (Berdasarkan Konfigurasi)
```
T+0s    : systemctl stop nginx di nginx1
T+2s    : Keepalived check script mendeteksi nginx down
T+2s    : Priority nginx1 turun dari 100 ke 80 (weight -20)
T+4s    : Keepalived check script konfirmasi nginx down (fall 2)
T+4s    : nginx1 melepas VIP ************
T+5s    : nginx2 mendeteksi nginx1 tidak mengirim VRRP advertisement
T+6s    : nginx2 mengambil alih VIP ************
T+6s    : nginx2 menjadi MASTER, nginx1 menjadi BACKUP
```

#### Konfigurasi Keepalived yang Mempengaruhi Failover Time
```
vrrp_script chk_nginx {
    script "/etc/keepalived/check_nginx.sh"
    interval 2          # Check setiap 2 detik
    weight -20          # Turunkan priority 20 poin jika fail
    fall 2              # Butuh 2 kali fail untuk declare down
    rise 1              # Butuh 1 kali success untuk declare up
}

vrrp_instance VI_1 {
    advert_int 1        # VRRP advertisement setiap 1 detik
    priority 100        # nginx1 priority (nginx2 = 90)
}
```

### Estimasi Downtime
- **Minimum Downtime**: 4-6 detik
- **Maximum Downtime**: 8-10 detik
- **Faktor yang Mempengaruhi**:
  - Check script interval (2s)
  - Fall threshold (2 kali)
  - VRRP advertisement interval (1s)
  - Network latency

## Analisis Response Time Selama Normal Operation

### Data Monitoring yang Dikumpulkan (17:35-17:36)
```
Response Time Distribution:
- Minimum: 0.153075s
- Maximum: 5.006486s  
- Average: ~1.2s
- Median: ~1.0s

Connection Time Distribution:
- Minimum: 0.076567s
- Maximum: 2.096713s
- Average: ~0.8s

Success Rate: 90% (18/20 requests)
Timeout Rate: 10% (2/20 requests)
```

### Pattern Analysis
1. **Normal Response**: 0.15s - 2.5s
2. **Slow Response**: 2.5s - 5s
3. **Timeout**: >5s (HTTP code 000)

## Prediksi Dampak Failover

### Skenario 1: Failover Sukses (Best Case)
```
T+0s  : nginx1 stop
T+1s  : Request masih ke nginx1 - FAIL (Connection refused)
T+2s  : Request masih ke nginx1 - FAIL (Connection refused)  
T+3s  : Request masih ke nginx1 - FAIL (Connection refused)
T+4s  : Request masih ke nginx1 - FAIL (Connection refused)
T+5s  : Request masih ke nginx1 - FAIL (Connection refused)
T+6s  : VIP pindah ke nginx2
T+7s  : Request ke nginx2 - SUCCESS (Response time normal)
```
**Estimated Downtime**: 6-7 detik

### Skenario 2: Failover dengan Delay (Worst Case)
```
T+0s  : nginx1 stop
T+1-8s: Requests fail (Connection refused/timeout)
T+9s  : VIP pindah ke nginx2 (dengan delay)
T+10s : Request ke nginx2 - SUCCESS
```
**Estimated Downtime**: 9-10 detik

## Rekomendasi Optimasi Failover

### 1. Tuning Keepalived Parameters
```
vrrp_script chk_nginx {
    script "/etc/keepalived/check_nginx.sh"
    interval 1          # Reduce dari 2s ke 1s
    weight -30          # Increase weight untuk faster failover
    fall 1              # Reduce dari 2 ke 1 untuk faster detection
    rise 1
}

vrrp_instance VI_1 {
    advert_int 1        # Keep 1s
    preempt_delay 5     # Add delay untuk prevent flapping
}
```

### 2. Optimasi Check Script
```bash
#!/bin/bash
# /etc/keepalived/check_nginx.sh
# Optimized nginx check script

# Quick port check
if ! nc -z localhost 80 2>/dev/null; then
    exit 1
fi

# HTTP response check
if ! curl -f -s -m 2 http://localhost/ >/dev/null 2>&1; then
    exit 1
fi

exit 0
```

### 3. Load Balancer Alternative
- **HAProxy + Keepalived**: Untuk load balancing aktif-aktif
- **Nginx Load Balancer**: Upstream configuration
- **Cloud Load Balancer**: Untuk environment cloud

## Kesimpulan Testing

### Yang Berhasil Diverifikasi
✅ **Konfigurasi HA**: Keepalived setup dengan VRRP  
✅ **Baseline Performance**: Response time 0.15s - 5s  
✅ **Monitoring Script**: Automated monitoring berfungsi  
✅ **Network Architecture**: NAT Mikrotik ke VIP  

### Yang Tidak Dapat Ditest
❌ **Failover Aktual**: Masalah routing VPN  
❌ **Recovery Time**: Tidak dapat test start nginx  
❌ **Real Downtime**: Tidak dapat measure actual downtime  

### Estimasi Berdasarkan Konfigurasi
- **Failover Time**: 6-10 detik
- **Detection Time**: 2-4 detik  
- **Switchover Time**: 2-3 detik
- **Recovery Time**: 1-2 detik (saat nginx1 kembali)

### Rekomendasi untuk Penelitian
1. **Fix Network Routing**: Untuk testing aktual
2. **Implement Monitoring**: Real-time monitoring dashboard
3. **Optimize Parameters**: Sesuai rekomendasi di atas
4. **Document Procedures**: SOP untuk maintenance
5. **Regular Testing**: Schedule failover testing

---
**Status**: Simulasi berdasarkan konfigurasi dan data monitoring yang berhasil dikumpulkan
